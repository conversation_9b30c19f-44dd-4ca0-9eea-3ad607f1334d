<script setup>
import { ref } from 'vue'
import ios from '../../Resources/download/ios.png'
import win from '../../Resources/download/win.png'
import andor from '../../Resources/download/andor.png'
import tv from '../../Resources/download/tv.png'
// 当前选中的tab
const activeTab = ref(0)

// tab数据
const tabs = [
  { id: 0, name: '电脑版' },
  { id: 1, name: '电视版' },
  { id: 2, name: '手机版' }
]

// 不同版本的下载按钮配置
const downloadConfig = {
  0: [ // 电脑版
    {
      text: '下载 MacOS 客户端',
      icon: ios,
      color: '#007AFF',
      url: '#'
    },
    {
      text: '下载 Windows客户端',
      icon: win,
      color: '#007AFF',
      url: '#'
    }
  ],
  1: [ // 电视版
    {
      text: '下载 TV 客户端',
      icon: tv,
      color: '#007AFF',
      url: '#'
    }
  ],
  2: [ // 手机版
    {
      text: '下载 iOS 客户端',
      icon: ios,
      color: '#007AFF',
      url: '#'
    },
    {
      text: '下载 Android 客户端',
      icon: andor,
      color: '#007AFF',
      url: '#'
    }
  ]
}

// 切换tab
const switchTab = (index) => {
  activeTab.value = index
}

// 下载处理
const handleDownload = (item) => {
  console.log('下载:', item.text)
  // 这里可以添加实际的下载逻辑
}
</script>

<template>
  <div class="download-center">
    <!-- 主要内容区域 -->
    <div class="content-wrapper">
      <!-- 标题 -->
      <h1 class="title">小米智能存储 下载中心</h1>

      <!-- Tab切换 -->
      <div class="tab-container">
        <div class="tab-wrapper">
          <div
            v-for="(tab, index) in tabs"
            :key="tab.id"
            class="tab-item"
            :class="{ active: activeTab === index }"
            @click="switchTab(index)"
          >
          <span>
            {{ tab.name }}
          </span>

          </div>
          <!-- 滑动指示器 -->
          <div
            class="tab-indicator"
            :style="{ transform: `translateX(${activeTab * 100}%)` }"
          ></div>
        </div>
      </div>

      <!-- 下载按钮区域 -->
      <div class="download-section">
        <transition-group name="fade" mode="out-in">
          <div
            v-for="(item, index) in downloadConfig[activeTab]"
            :key="`${activeTab}-${index}`"
            class="download-btn-wrapper"
          >
            <div
              class="download-btn"
              :style="{ backgroundColor: item.color }"
              @click="handleDownload(item)"
            >
              <img :src="item.icon" class="btn-icon"  alt="">
              <span class="btn-text">{{ item.text }}</span>
            </div>
          </div>
        </transition-group>
      </div>

      <!-- 展示图片 -->
      <div class="image-section">
        <div class="image-container">
          <!-- 模拟的界面截图 -->
          <div class="mock-interface">
            <!-- 浏览器顶部 -->
            <div class="browser-header">
              <div class="browser-controls">
                <span class="control-btn red"></span>
                <span class="control-btn yellow"></span>
                <span class="control-btn green"></span>
              </div>
              <div class="address-bar">
                <span class="url-text">https://storage.mi.com</span>
              </div>
              <div class="browser-icons">
                <span class="icon">⭐</span>
                <span class="icon">🔒</span>
                <span class="icon">⚙️</span>
                <span class="icon">👤</span>
              </div>
            </div>

            <!-- 主界面内容 -->
            <div class="interface-content">
              <!-- 左侧边栏 -->
              <div class="sidebar">
                <div class="logo-section">
                  <div class="logo">MI</div>
                  <span class="logo-text">智能存储</span>
                </div>
                <div class="nav-items">
                  <div class="nav-item active">📁 我的文件</div>
                  <div class="nav-item">📷 相册</div>
                  <div class="nav-item">🎵 音乐</div>
                  <div class="nav-item">🎬 视频</div>
                  <div class="nav-item">📄 文档</div>
                </div>
              </div>

              <!-- 主内容区 -->
              <div class="main-content">
                <!-- 顶部工具栏 -->
                <div class="toolbar">
                  <div class="breadcrumb">我的文件 / 图片</div>
                  <div class="view-controls">
                    <span class="view-btn">⊞</span>
                    <span class="view-btn active">⊟</span>
                    <span class="search-icon">🔍</span>
                  </div>
                </div>

                <!-- 文件网格 -->
                <div class="file-grid">
                  <div class="file-item" v-for="i in 8" :key="i">
                    <div class="file-thumbnail" :class="`thumb-${i}`"></div>
                    <div class="file-name">图片_{{ i }}.jpg</div>
                  </div>
                </div>
              </div>

              <!-- 右侧信息面板 -->
              <div class="info-panel">
                <div class="panel-title">文件信息</div>
                <div class="file-details">
                  <div class="detail-item">
                    <span class="label">大小:</span>
                    <span class="value">2.5 MB</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">类型:</span>
                    <span class="value">JPEG 图片</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">修改时间:</span>
                    <span class="value">2024-01-15</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.download-center {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 40px 0;
}

.content-wrapper {
  max-width: 60%;
  margin: 0 auto;
  padding: 0 20px;
}

/* 标题样式 */
.title {
  text-align: center;
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 40px;
  letter-spacing: 1px;
}

/* Tab切换样式 */
.tab-container {
  display: flex;
  height: 60px;
  justify-content: center;
  margin-bottom: 40px;
}

.tab-wrapper {
  position: relative;
  display: flex;
  justify-content: space-between;
  background: #ECEFF5;
  border-radius: 25px;
  padding: 4px;
  width: 480px;
  height: 60px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.tab-item {
  position: relative;
  padding: 12px 24px;
  cursor: pointer;
  /* width: 135px; */
  font-size: 16px;
  font-weight: 500;
  /* color: #666; */
  transition: all 0.3s ease;
  border-radius: 20px;
  z-index: 2;
  min-width: 80px;
  text-align: center;
}

.tab-item.active {
  color: #000;
  background-color: #fff;
}

.tab-indicator {
  position: absolute;
  top: 4px;
  left: 4px;
  width: calc(33.333% - 4px);
  height: calc(100% - 8px);
  background: #ECEFF5;
  border-radius: 20px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

/* 下载按钮区域 */
.download-section {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 50px;
  flex-wrap: wrap;
}

.download-btn-wrapper {
  transition: all 0.3s ease;
}

.download-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 32px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  min-width: 200px;
  justify-content: center;
}

.download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.download-btn:active {
  transform: translateY(0);
}

.btn-icon {
  height: 40px;
  width: 40px;
}

.btn-text {
  font-size: 16px;
}

/* 图片展示区域 */
.image-section {
  display: flex;
  justify-content: center;
}

.image-container {
  max-width: 100%;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
}

.image-container:hover {
  transform: scale(1.02);
}

.preview-image {
  width: 100%;
  height: auto;
  display: block;
}

/* 模拟界面样式 */
.mock-interface {
  width: 800px;
  height: 500px;
  background: #f5f5f5;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.browser-header {
  height: 40px;
  background: #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 15px;
  border-bottom: 1px solid #ddd;
}

.browser-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.control-btn.red { background: #ff5f57; }
.control-btn.yellow { background: #ffbd2e; }
.control-btn.green { background: #28ca42; }

.address-bar {
  flex: 1;
  margin: 0 20px;
  background: white;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  color: #666;
}

.browser-icons {
  display: flex;
  gap: 10px;
}

.icon {
  font-size: 14px;
  opacity: 0.7;
}

.interface-content {
  display: flex;
  height: calc(100% - 40px);
}

.sidebar {
  width: 200px;
  background: #2c3e50;
  color: white;
  padding: 20px 0;
}

.logo-section {
  display: flex;
  align-items: center;
  padding: 0 20px;
  margin-bottom: 30px;
}

.logo {
  width: 32px;
  height: 32px;
  background: #3498db;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  margin-right: 10px;
}

.logo-text {
  font-size: 16px;
  font-weight: 600;
}

.nav-items {
  padding: 0 10px;
}

.nav-item {
  padding: 12px 15px;
  margin: 2px 0;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
  background: #3498db;
}

.main-content {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
}

.toolbar {
  height: 50px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.breadcrumb {
  font-size: 14px;
  color: #666;
}

.view-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.view-btn {
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  opacity: 0.6;
}

.view-btn.active {
  background: #e9ecef;
  opacity: 1;
}

.search-icon {
  font-size: 16px;
  opacity: 0.6;
  margin-left: 10px;
}

.file-grid {
  flex: 1;
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  overflow-y: auto;
}

.file-item {
  text-align: center;
  cursor: pointer;
}

.file-thumbnail {
  width: 100%;
  height: 80px;
  border-radius: 8px;
  margin-bottom: 8px;
  background: linear-gradient(45deg, #667eea, #764ba2);
}

.file-thumbnail.thumb-1 { background: linear-gradient(45deg, #ff9a9e, #fecfef); }
.file-thumbnail.thumb-2 { background: linear-gradient(45deg, #a8edea, #fed6e3); }
.file-thumbnail.thumb-3 { background: linear-gradient(45deg, #ffecd2, #fcb69f); }
.file-thumbnail.thumb-4 { background: linear-gradient(45deg, #667eea, #764ba2); }
.file-thumbnail.thumb-5 { background: linear-gradient(45deg, #f093fb, #f5576c); }
.file-thumbnail.thumb-6 { background: linear-gradient(45deg, #4facfe, #00f2fe); }
.file-thumbnail.thumb-7 { background: linear-gradient(45deg, #43e97b, #38f9d7); }
.file-thumbnail.thumb-8 { background: linear-gradient(45deg, #fa709a, #fee140); }

.file-name {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info-panel {
  width: 250px;
  background: #f8f9fa;
  border-left: 1px solid #e9ecef;
  padding: 20px;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}



.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 14px;
}

.label {
  color: #666;
}

.value {
  color: #333;
  font-weight: 500;
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-wrapper {
    max-width: 90%;
    padding: 0 15px;
  }

  .title {
    font-size: 24px;
    margin-bottom: 30px;
  }

  .tab-item {
    padding: 10px 16px;
    font-size: 14px;
    min-width: 60px;
  }

  .download-section {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .download-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 250px;
    padding: 14px 24px;
  }
}

@media (max-width: 480px) {
  .content-wrapper {
    max-width: 95%;
  }

  .title {
    font-size: 22px;
  }

  .tab-wrapper {
    padding: 3px;
  }

  .tab-item {
    display: flex;
    align-items: center;
    text-align: center;
  }
  .download-section{
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

  }
  .download-btn-wrapper{
    width: 150px;
  }
  .download-btn{
    width: 150px;
  }

  .btn-icon{
    width: 12px;
    height: 12px;
  }
  .btn-text{
    font-size: 12px;
  }
}
</style>