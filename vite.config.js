import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import pxtorem from 'postcss-pxtorem'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  base: process.env.VITE_BASE_URL || '/nas-web/', // 设置项目的基础路径
  css: {
        postcss: {
      mode: "extends",
      loaderOptions: {
        postcssOptions: {
          plugins: [
            [
              "postcss-pxtorem",
              {
                rootValue: 39.2, // 设计稿宽度/10（如 375px 设计稿设为 37.5）
                propList: ["*"], // 转换所有 CSS 属性的 px 单位
                unitPrecision: 5, // 保留小数点位数
                minPixelValue: 1, // 最小转换像素值
                exclude: /node_modules/i,
                selectorBlackList: ["ignore-"],
                mediaQuery: false,
                replace: true
              },
            ],
          ],
        },
      },
    },
  },
})
