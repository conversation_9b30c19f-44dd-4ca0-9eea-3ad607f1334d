<script setup>
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import { showToast, showDialog } from "vant";
import user from "../../Resources/share/user-test.png";

const route = useRoute();
const isValid = ref(false);
const loading = ref(true);
const showDownloadPage = ref(false);

// 设备信息
const deviceInfo = ref({
  id: "",
  name: "",
  num: 0,
  nickName: "",
});

// Mock检测app是否安装 (实际项目中需要替换为真实的检测逻辑)
const checkAppInstalled = () => {
  // 这里可以通过尝试打开app scheme来检测
  // 目前先mock一个随机结果用于演示
  return Math.random() > 0.5; // 50%概率返回已安装
};

// 处理接受分享按钮点击
const handleAcceptShare = () => {
  const isAppInstalled = checkAppInstalled();

  if (isAppInstalled) {
    // 已安装app，显示原有的对话框流程
    showDialog({
      title: "",
      message: '即将离开微信，打开"小米智能存储"',
      showCancelButton: true,
      cancelButtonText: "取消",
      confirmButtonText: "允许",
      className: "custom-dialog",
      beforeClose: (action) => {
        if (action === "confirm") {
          // 使用链接方式唤起应用
          openAppWithLink();
          return true;
        }
        return true;
      },
    });
  } else {
    // 未安装app，显示下载页面
    showDownloadPage.value = true;
  }
};

// 处理下载按钮点击
const handleDownload = (platform) => {
  if (platform === 'android') {
    // 跳转到安卓下载链接
    window.open('https://app.mi.com/details?id=com.xiaomi.smarthome', '_blank');
  } else if (platform === 'ios') {
    // 跳转到iOS下载链接
    window.open('https://apps.apple.com/cn/app/xiaomi-home/id957323480', '_blank');
  }
};

// 返回分享页面
const backToShare = () => {
  showDownloadPage.value = false;
};

// 使用链接方式唤起应用
const openAppWithLink = () => {
  try {
    // 创建一个隐藏的a标签
    const appLink = document.createElement("a");
    appLink.rel = "nofollow";
    appLink.href = "xiaomi://nas/open?";
    appLink.style.display = "none";
    document.body.appendChild(appLink);

    // 模拟点击链接
    appLink.click();

    // 移除链接元素
    setTimeout(() => {
      document.body.removeChild(appLink);
      showToast({
        message: "已尝试打开应用",
        position: "bottom",
      });
    }, 500);
  } catch (error) {
    console.error("打开应用失败:", error);
    showToast({
      message: "打开应用失败，请手动打开",
      position: "bottom",
    });
  }
};

// 检查分享是否过期（4小时有效期）
const checkShareExpired = (timestamp) => {
  if (!timestamp) return true;

  try {
    // 解析秒时间戳
    const timestampNum = parseInt(timestamp);

    // 检查时间戳是否有效
    if (isNaN(timestampNum)) {
      console.error("无效的时间戳格式:", timestamp);
      return true;
    }

    // 将秒时间戳转换为毫秒时间戳
    const shareTime = new Date(timestampNum * 1000);
    const currentTime = new Date();

    // 计算时间差（毫秒）
    const timeDiff = currentTime - shareTime;

    // 4小时 = 4 * 60 * 60 * 1000 毫秒
    const fourHours = 4 * 60 * 60 * 1000;

    return timeDiff > fourHours;
  } catch (error) {
    console.error("解析时间戳失败:", error);
    return true; // 解析失败当作过期处理
  }
};

onMounted(async () => {
  try {
    // 获取URL参数
    const { sharecode, timestamp, deviceName, nickName, deviceNum } =
      route.query;

    if (!sharecode || !timestamp) {
      showToast("分享链接参数不完整");
      isValid.value = false;
      loading.value = false;
      return;
    }

    // 检查分享是否过期
    const expired = checkShareExpired(timestamp);

    if (expired) {
      isValid.value = false;
      loading.value = false;
      return;
    }

    // 分享有效
    isValid.value = true;
    deviceInfo.value = {
      id: nickName || "未知用户",
      name: deviceName || "设备",
      num: parseInt(deviceNum) || 1,
      nickName: nickName || "未知用户",
    };

    loading.value = false;
  } catch (error) {
    console.error("验证分享链接失败:", error);
    showToast("验证分享链接失败");
    isValid.value = false;
    loading.value = false;
  }
});
</script>

<template>
  <div class="share-page">
    <van-loading v-if="loading" class="loading" vertical
      >验证分享链接中...</van-loading
    >

    <!-- 无效分享页面 -->
    <div v-if="!loading && !isValid" class="share-content invalid-share">
      <div class="title">小米智能存储</div>

      <div class="logo-container">
        <van-image src="" alt="Logo" class="logo" />
      </div>
      <div class="status-text">共享失效</div>
      <div class="desc-text">该共享已过有效期</div>
      <div class="button-container">
        <van-button
          type="primary"
          class="action-button"
          @click=""
          >返回</van-button
        >
      </div>
    </div>

    <!-- 有效分享页面 -->
    <div v-if="!loading && isValid" class="share-content valid-share">
      <div class="title">小米智能存储</div>
      <div class="device-container">
        <van-image src="" alt="设备" class="device-image" />
      </div>
      <div class="avatar-container">
        <van-image round width="50" height="50" :src="user" />
      </div>
      <div class="device-info">
        {{ deviceInfo.nickName }}分享了{{ deviceInfo.num }}个{{
          deviceInfo.name
        }}给您
      </div>
      <div class="device-desc">
        接受邀请后您可与{{ deviceInfo.nickName }}共同使用Xiaomi智能存储。
      </div>
      <div class="button-container">
        <van-button
          type="primary"
          class="action-button"
          @click="handleAcceptShare"
          >接受分享</van-button
        >
      </div>
    </div>

    <!-- 下载页面 -->
    <div v-if="showDownloadPage" class="download-page">
      <div class="download-content">
        <div class="title">小米智能存储</div>

        <div class="download-icon-container">
          <div class="download-icon"></div>
        </div>

        <div class="download-title">小米智能存储</div>
        <div class="download-desc">欢迎使用小米智能存储</div>

        <div class="download-buttons">
          <van-button
            class="download-button android-button"
            @click="handleDownload('android')"
          >
            安卓版下载
          </van-button>
          <van-button
            class="download-button ios-button"
            @click="handleDownload('ios')"
          >
            iOS版下载
          </van-button>
        </div>
      </div>
    </div>

    <!-- test -->
    <div class="test-links" style="margin-top: 20px; display: none">
      <a rel="nofollow" href="xiaomi://nas/open?">直接打开应用</a>
    </div>
  </div>
</template>

<style scoped>
.share-page {
  min-height: calc(100% - 35px);
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #ffffff;
  color: #333;
  transition: background-color 0.3s, color 0.3s;
  padding-top: 35px;
}
.title {
  font-family: Albert Sans;
  font-weight: 500;
  font-style: Medium;
  font-size: 20px;
  line-height: 100%;
  letter-spacing: 0px;
  color: #000;
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.share-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 24px;
  box-sizing: border-box;
  background-color: #ffffff;
  transition: background-color 0.3s;
}

.logo-container,
.device-container {
  margin-bottom: 53px;
  text-align: center;
}

.logo {
  width: 150px;
  height: 150px;
  border-radius: 45px;
  margin-top: 79px;
}

.device-image {
  width: 150px;
  height: 150px;
  border-radius: 45px;
  margin-top: 79px;
}

.avatar-container {
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
}

.status-text {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
  text-align: center;
}

.desc-text,
.device-info,
.device-desc {
  font-family: Albert Sans;
  font-weight: 500;
  font-style: Medium;
  line-height: 100%;
  letter-spacing: 0px;
  padding: 0 30px;
}

.device-info {
  font-size: 20px;
  line-height: 100%;
  color: #000;
}

.device-desc,.desc-text {
  color: rgba(0, 0, 0, 0.5);
  font-size: 16px;
  margin-bottom: 30px;
  margin-top: 12px;
}

.button-container {
  width: 100%;
  display: flex;
  justify-content: center;
  position: fixed;
  bottom: 28px;
  left: 0;
  right: 0;
  padding: 0 24px;
  box-sizing: border-box;
}

.action-button {
  width: 100%;
  /* max-width: 300px; */
  height: 44px;
  border-radius: 22px;
}

.custom-dialog .van-dialog {
  width: 80%;
  max-width: 320px;
  border-radius: 12px;
  overflow: hidden;
}

.custom-dialog .van-dialog__content {
  padding: 20px 16px;
}

.custom-dialog .van-dialog__message {
  font-size: 16px;
  font-weight: 400;
  padding: 8px 0;
}

.custom-dialog .van-dialog__footer {
  display: flex;
}

.custom-dialog .van-button {
  flex: 1;
  border: none;
  height: 44px;
  font-size: 16px;
}

.custom-dialog .van-dialog__cancel {
  color: #666;
}

.custom-dialog .van-dialog__confirm {
  color: #1989fa;
  font-weight: 500;
}

.custom-dialog .van-hairline--top::after {
  border-top-color: #ebedf0;
}

.custom-dialog .van-dialog__footer::after {
  border-color: #ebedf0;
}

@media (prefers-color-scheme: dark) {
  /* 暗色模式下的Dialog样式 */
  .custom-dialog .van-dialog {
    background-color: #1c1c1e;
  }

  .custom-dialog .van-dialog__message {
    color: #f5f5f5;
  }

  .custom-dialog .van-dialog__cancel {
    color: #aaaaaa;
  }

  .custom-dialog .van-hairline--top::after {
    border-top-color: #2c2c2e;
  }

  .custom-dialog .van-dialog__footer::after {
    border-color: #2c2c2e;
  }

  /* Vant组件在暗色模式下的样式覆盖 */
  .van-toast {
    background-color: #2c2c2e;
    color: #f5f5f5;
  }

  .van-loading__text {
    color: #f5f5f5;
  }

  .share-page {
    background-color: #121212;
    color: #f5f5f5;
  }

  .share-content {
    background-color: #121212;
  }

  .status-text {
    color: #f5f5f5;
  }

  .desc-text,
  .device-desc {
    color: #aaaaaa;
  }

  .device-info {
    color: #e0e0e0;
  }
}

/* 下载页面样式 */
.download-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  z-index: 1000;
}

.download-content {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 60px 40px 40px;
  box-sizing: border-box;
}

.download-icon-container {
  margin-bottom: 40px;
  display: flex;
  justify-content: center;
  margin-top: 109px;
}

.download-icon {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #ff8c42 0%, #ff6b1a 100%);
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.download-title {
  font-size: 20px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  text-align: center;
}

.download-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 50px;
  text-align: center;
}

.download-buttons {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0 24px;
  box-sizing: border-box;
}

.download-button {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  border: none;
}

.android-button {
  background-color: #f5f5f5;
  color: #333;
}

.ios-button {
  background-color: #f5f5f5;
  color: #333;
}

</style>
